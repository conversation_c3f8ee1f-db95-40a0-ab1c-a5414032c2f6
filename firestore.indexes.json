{"indexes": [{"collectionGroup": "ingredients", "queryScope": "COLLECTION", "fields": [{"fieldPath": "familyId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "ingredients", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "pantry", "queryScope": "COLLECTION", "fields": [{"fieldPath": "familyId", "order": "ASCENDING"}, {"fieldPath": "ingredientName", "order": "ASCENDING"}]}, {"collectionGroup": "pantry", "queryScope": "COLLECTION", "fields": [{"fieldPath": "familyId", "order": "ASCENDING"}, {"fieldPath": "expiryDate", "order": "ASCENDING"}]}, {"collectionGroup": "pantry", "queryScope": "COLLECTION", "fields": [{"fieldPath": "familyId", "order": "ASCENDING"}, {"fieldPath": "purchaseDate", "order": "DESCENDING"}]}], "fieldOverrides": []}