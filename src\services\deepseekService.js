/**
 * DeepSeek AI Service for Recipe Generation
 * 
 * IMPORTANT: Replace 'YOUR_DEEPSEEK_API_KEY_HERE' with your actual DeepSeek API key
 * Get your API key from: https://platform.deepseek.com/
 */

const DEEPSEEK_API_KEY = '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'; // ⚠️ REPLACE WITH YOUR ACTUAL API KEY
const DEEPSEEK_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

/**
 * Generate a complete recipe using DeepSeek AI
 * @param {string} recipeName - The name of the recipe to generate
 * @param {Array} availableIngredients - List of available ingredients from the database
 * @returns {Promise<Object>} Generated recipe data
 */
export const generateRecipeWithDeepSeek = async (recipeName, availableIngredients = []) => {
  if (!DEEPSEEK_API_KEY || DEEPSEEK_API_KEY === '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') {
    throw new Error('Clé API DeepSeek non configurée. Veuillez configurer votre clé API dans src/services/deepseekService.js');
  }

  if (!recipeName || recipeName.trim().length < 3) {
    throw new Error('Le nom de la recette doit contenir au moins 3 caractères');
  }

  // Create ingredient list for AI context
  const ingredientList = availableIngredients.length > 0 
    ? availableIngredients.map(ing => `${ing.name} (${ing.unit})`).join(', ')
    : 'Utilisez des ingrédients typiques de la cuisine camerounaise et africaine';

  const prompt = `Génère une recette complète en français pour "${recipeName}". 
Focus sur la cuisine camerounaise et africaine. Utilise de préférence ces ingrédients disponibles: ${ingredientList}.

Réponds UNIQUEMENT avec un objet JSON valide dans ce format exact:
{
  "description": "Description appétissante de la recette (10-100 mots)",
  "prepTime": 30,
  "cookTime": 45,
  "servings": 6,
  "difficulty": "Facile|Moyen|Difficile",
  "cuisine": "camerounaise",
  "categories": ["Plat Principal", "Traditionnel"],
  "ingredients": [
    {
      "name": "Nom de l'ingrédient",
      "quantity": 500,
      "unit": "g",
      "category": "Légumes"
    }
  ],
  "instructions": [
    "Étape 1: Description détaillée de la première étape",
    "Étape 2: Description détaillée de la deuxième étape"
  ],
  "tips": [
    "Conseil utile pour réussir la recette"
  ],
  "nutrition": {
    "calories": 350,
    "protein": 25,
    "carbs": 40,
    "fat": 12,
    "fiber": 8
  }
}

Assure-toi que:
- Les quantités sont réalistes pour ${recipeName}
- Les unités correspondent aux ingrédients (g, kg, ml, l, cuillères, etc.)
- Les instructions sont claires et détaillées (minimum 3 étapes)
- La difficulté correspond à la complexité réelle
- Les catégories sont appropriées
- Les valeurs nutritionnelles sont cohérentes`;

  try {
    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: 'Tu es un chef expert en cuisine camerounaise et africaine. Tu génères des recettes authentiques et détaillées en français. Réponds toujours avec du JSON valide uniquement.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.7,
        stream: false
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Erreur API DeepSeek: ${response.status} - ${errorData.error?.message || 'Erreur inconnue'}`);
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Réponse API invalide: structure de données manquante');
    }

    const content = data.choices[0].message.content.trim();
    
    // Extract JSON from response (in case there's extra text)
    let jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('Aucun JSON valide trouvé dans la réponse de l\'IA');
    }

    const generatedRecipe = JSON.parse(jsonMatch[0]);
    
    // Validate required fields
    const requiredFields = ['description', 'prepTime', 'cookTime', 'servings', 'difficulty', 'ingredients', 'instructions'];
    for (const field of requiredFields) {
      if (!generatedRecipe[field]) {
        throw new Error(`Champ requis manquant dans la recette générée: ${field}`);
      }
    }

    // Validate ingredients array
    if (!Array.isArray(generatedRecipe.ingredients) || generatedRecipe.ingredients.length === 0) {
      throw new Error('La liste des ingrédients est invalide ou vide');
    }

    // Validate instructions array
    if (!Array.isArray(generatedRecipe.instructions) || generatedRecipe.instructions.length === 0) {
      throw new Error('La liste des instructions est invalide ou vide');
    }

    // Ensure all ingredients have required fields
    generatedRecipe.ingredients = generatedRecipe.ingredients.map(ingredient => ({
      name: ingredient.name || 'Ingrédient inconnu',
      quantity: parseFloat(ingredient.quantity) || 1,
      unit: ingredient.unit || 'unité',
      category: ingredient.category || 'Autres'
    }));

    // Ensure numeric fields are numbers
    generatedRecipe.prepTime = parseInt(generatedRecipe.prepTime) || 30;
    generatedRecipe.cookTime = parseInt(generatedRecipe.cookTime) || 45;
    generatedRecipe.servings = parseInt(generatedRecipe.servings) || 4;

    // Set default values for optional fields
    generatedRecipe.cuisine = generatedRecipe.cuisine || 'camerounaise';
    generatedRecipe.categories = Array.isArray(generatedRecipe.categories) ? generatedRecipe.categories : ['Plat Principal'];
    generatedRecipe.tips = Array.isArray(generatedRecipe.tips) ? generatedRecipe.tips : [];
    
    // Ensure nutrition object exists
    if (!generatedRecipe.nutrition || typeof generatedRecipe.nutrition !== 'object') {
      generatedRecipe.nutrition = {
        calories: 300,
        protein: 20,
        carbs: 35,
        fat: 10,
        fiber: 5
      };
    }

    // Add metadata
    generatedRecipe.aiGenerated = true;
    generatedRecipe.generatedAt = new Date().toISOString();
    generatedRecipe.generatedBy = 'DeepSeek AI';

    return generatedRecipe;

  } catch (error) {
    console.error('DeepSeek API Error:', error);
    
    // Provide specific error messages
    if (error.message.includes('fetch')) {
      throw new Error('Erreur de connexion à l\'API DeepSeek. Vérifiez votre connexion internet.');
    }
    
    if (error.message.includes('401') || error.message.includes('403')) {
      throw new Error('Clé API DeepSeek invalide. Vérifiez votre clé API.');
    }
    
    if (error.message.includes('429')) {
      throw new Error('Limite de taux API atteinte. Veuillez réessayer dans quelques minutes.');
    }
    
    if (error.message.includes('JSON')) {
      throw new Error('Erreur de format dans la réponse de l\'IA. Veuillez réessayer.');
    }

    // Re-throw the error with original message if it's already user-friendly
    throw error;
  }
};

/**
 * Test the DeepSeek API connection
 * @returns {Promise<boolean>} True if API is working
 */
export const testDeepSeekConnection = async () => {
  try {
    await generateRecipeWithDeepSeek('Test de connexion', []);
    return true;
  } catch (error) {
    console.error('DeepSeek connection test failed:', error);
    return false;
  }
};

/**
 * Get API key status
 * @returns {Object} API key configuration status
 */
export const getApiKeyStatus = () => {
  return {
    configured: DEEPSEEK_API_KEY && DEEPSEEK_API_KEY !== '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',
    placeholder: DEEPSEEK_API_KEY === '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'
  };
};
